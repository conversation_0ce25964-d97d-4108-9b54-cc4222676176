# 合并请求代码审查工具

这是一个Python工具，可以从阿里云CodeUp的合并请求URL自动获取代码diff并进行AI代码审查。

## 功能特性

1. **URL解析**: 自动解析CodeUp合并请求URL
2. **Repository查找**: 根据项目路径自动查找repository ID
3. **Commit获取**: 获取合并请求中的所有commit ID
4. **Diff比较**: 获取代码变更的详细diff
5. **AI审查**: 使用AI模型进行专业的代码审查
6. **自定义模型**: 支持通过命令行参数指定AI模型
7. **自定义提示词**: 支持从本地文件加载系统提示词

## 安装依赖

```bash
pip install -r requirements.txt
```

## 环境配置

设置以下环境变量（可选，有默认值）：

```bash
export XM_LLM_API_KEY="your-api-key"
export XM_LLM_BASE_URL="https://test-one-api.summerfarm.top/v1"
export XM_LLM_MODEL="deepseek-v3-250324"
```

## 使用方法

### 交互式使用

1. 运行程序：
```bash
python mr_code_review.py
```

2. 输入CodeUp合并请求URL，例如：
```
https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs
```

### 命令行使用

```bash
# 使用默认模型
python mr_code_review.py --url "https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"

# 指定特定模型
python mr_code_review.py --model "gpt-4" --url "https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"

# 仅指定模型（交互式输入URL）
python mr_code_review.py --model "claude-3-sonnet"

# 查看帮助信息
python mr_code_review.py --help
```

### 命令行参数

- `--model MODEL`: 指定AI模型（可选，默认使用环境变量 `XM_LLM_MODEL`）
- `--url URL`: 指定CodeUp合并请求URL（可选，不指定则交互式输入）
- `--help`: 显示帮助信息

### 程序执行流程

程序会自动：
   - 解析URL获取组织ID、项目路径和合并请求ID
   - 查找对应的repository ID
   - 获取合并请求中的所有commit
   - 比较第一个和最后一个commit的差异
   - 对每个变更文件进行AI代码审查
   - 生成审查报告并保存为Markdown文件

## 自定义系统提示词

工具会自动从 `code_review_instruction.txt` 文件加载系统提示词。你可以编辑这个文件来自定义AI的审查行为：

1. 编辑 `code_review_instruction.txt` 文件
2. 重新运行工具，新的提示词会自动生效

如果文件不存在，工具会使用内置的默认提示词。

## 输出结果

程序会生成一个Markdown格式的审查报告，文件名格式为：
```
mr_code_review_{项目路径}_{合并请求ID}.md
```

报告包含：
- 合并请求URL
- 使用的AI模型信息
- 每个变更文件的详细审查结果
- AI识别的关键问题和建议

## URL格式说明

支持的URL格式：
```
https://codeup.aliyun.com/{organization_id}/{namespace}/{project_name}/change/{merge_request_id}/diffs
```

例如：
- `organization_id`: 6189f099041d450d2c253abc
- `namespace`: tech
- `project_name`: xianmu-ai
- `merge_request_id`: 255

## 注意事项

1. 需要配置阿里云访问凭据
2. 确保有访问对应CodeUp项目的权限
3. AI审查重点关注严重Bug、安全漏洞、性能问题和架构缺陷
4. 程序会忽略次要的代码风格问题
5. 系统提示词文件 `code_review_instruction.txt` 应与脚本在同一目录

## 错误处理

程序包含完善的错误处理机制：
- URL格式验证
- API调用异常处理
- 网络请求超时处理
- 空数据检查

如果遇到问题，请检查：
1. URL格式是否正确
2. 网络连接是否正常
3. 阿里云凭据是否配置正确
4. 是否有访问项目的权限
5. 系统提示词文件是否存在且可读
