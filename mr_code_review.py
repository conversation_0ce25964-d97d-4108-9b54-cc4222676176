#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并请求代码审查工具
从阿里云CodeUp URL解析合并请求信息，获取diff并进行AI代码审查
"""

import os
import re
import json
import requests
import argparse
from typing import Optional, Dict, Any

# 阿里云DevOps SDK导入
from alibabacloud_devops20210625.client import Client as devops20210625Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_devops20210625 import models as devops_20210625_models
from alibabacloud_tea_util import models as util_models

# LLM配置
LLM_API_KEY = os.getenv("XM_LLM_API_KEY", "sk-")
LLM_BASE_URL = os.getenv("XM_LLM_BASE_URL", "https://test-one-api.summerfarm.top/v1")
LLM_MODEL = os.getenv("XM_LLM_MODEL", "deepseek-v3-250324")

def load_system_instruction(file_path: str = "code_review_instruction.txt") -> str:
    """从文件加载系统指令，如果文件不存在则使用默认指令"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"警告: 未找到系统指令文件 {file_path}，使用默认指令")
        # 默认指令作为备用
        return """你是一位经验丰富的资深代码评审专家。你将收到两部分信息：Git diff 输出和完整文件内容。请严格分析这些信息，只识别并报告最关键的问题。你的评审应重点关注：

1.  **严重 Bug：** 直接通过更改可识别的逻辑错误、崩溃或数据损坏风险。
2.  **重大安全漏洞：** 在代码中可见的常见弱点（例如，注入缺陷、访问控制失效、敏感数据暴露）。
3.  **主要性能瓶颈：** 引入显著低效的代码更改（例如，N+1 查询、处理大型数据集的低效循环），影响资源使用或延迟。
4.  **关键架构/设计缺陷：** 根据提供的上下文，引入显著耦合、违反核心设计原则或严重阻碍未来维护/扩展性的更改。

**重要指示：**
*   【一定要遵守】请使用中文回答。
*   从代码中推断编程语言，重点分析 diff 中的变更。
*   **严格忽略：** 次要代码风格问题、小错字、注释、文档、次要优化以及非关键建议。
*   按严重程度优先报告问题。
*   直接、专业、简洁。
*   如果根据这些标准**未**找到任何关键问题，则**只**回复："恭喜🎉！未发现任何严重问题。"
"""
    except Exception as e:
        print(f"读取系统指令文件时发生错误: {e}，使用默认指令")
        return "你是一位经验丰富的代码评审专家，请对提供的代码diff进行审查，重点关注严重bug、安全漏洞、性能问题和架构缺陷。请使用中文回答。"


class DevOpsClient:
    """阿里云DevOps客户端封装"""
    
    def __init__(self):
        self.client = self._create_client()
    
    @staticmethod
    def _create_client() -> devops20210625Client:
        """创建DevOps客户端"""
        credential = CredentialClient()
        config = open_api_models.Config(credential=credential)
        config.endpoint = 'devops.cn-hangzhou.aliyuncs.com'
        return devops20210625Client(config)
    
    def find_repository_by_path(self, organization_id: str, path_with_namespace: str) -> Optional[str]:
        """根据路径查找repository ID"""
        try:
            request = devops_20210625_models.ListRepositoriesRequest(
                organization_id=organization_id
            )
            runtime = util_models.RuntimeOptions()
            headers = {}
            
            response = self.client.list_repositories_with_options(request, headers, runtime)
            
            if response.body and response.body.result:
                for repo in response.body.result:
                    if hasattr(repo, 'path_with_namespace') and repo.path_with_namespace == path_with_namespace:
                        return str(repo.id)
            
            print(f"未找到匹配的repository: {path_with_namespace}")
            return None
            
        except Exception as e:
            print(f"查找repository时发生错误: {e}")
            return None
    
    def get_merge_request_commits(self, organization_id: str, repository_id: str, local_id: int) -> Dict[str, Optional[str]]:
        """
        获取合并请求的commit ID，返回MERGE_SOURCE和MERGE_TARGET
        MERGE_SOURCE: 新代码（要被合并的分支）
        MERGE_TARGET: 旧代码（目标分支，通常是主分支）
        """
        try:
            request = devops_20210625_models.ListMergeRequestPatchSetsRequest(
                organization_id=organization_id,
                repository_identity=repository_id,
                local_id=local_id
            )
            runtime = util_models.RuntimeOptions()
            headers = {}

            response = self.client.list_merge_request_patch_sets_with_options(request, headers, runtime)
            print(f"获取合并请求的所有commit ID response: {response}")

            merge_source_commit = None
            merge_target_commit = None

            if response.body and response.body.result:
                for patch_set in response.body.result:
                    if hasattr(patch_set, 'commit_id') and hasattr(patch_set, 'related_merge_item_type'):
                        if patch_set.related_merge_item_type == 'MERGE_SOURCE':
                            merge_source_commit = patch_set.commit_id
                        elif patch_set.related_merge_item_type == 'MERGE_TARGET':
                            merge_target_commit = patch_set.commit_id

            return {
                'merge_source': merge_source_commit,
                'merge_target': merge_target_commit
            }

        except Exception as e:
            print(f"获取合并请求commits时发生错误: {e}")
            return {'merge_source': None, 'merge_target': None}
    
    def get_compare_detail(self, repository_id: str, organization_id: str, from_commit: str, to_commit: str) -> Optional[Dict[str, Any]]:
        """获取两个commit之间的diff详情"""
        try:
            request = devops_20210625_models.GetCompareDetailRequest(
                from_=from_commit,
                to=to_commit,
                organization_id=organization_id
            )
            runtime = util_models.RuntimeOptions()
            headers = {}

            response = self.client.get_compare_detail_with_options(repository_id, request, headers, runtime)

            if response.body and response.body.result:
                return {
                    'diffs': response.body.result.diffs if hasattr(response.body.result, 'diffs') else [],
                    'commits': response.body.result.commits if hasattr(response.body.result, 'commits') else []
                }

            return None

        except Exception as e:
            print(f"获取diff详情时发生错误: {e}")
            return None

    def get_file_content(self, repository_id: str, organization_id: str, file_path: str, ref: str = 'master') -> Optional[str]:
        """获取指定文件的内容"""
        try:
            request = devops_20210625_models.GetFileBlobsRequest(
                organization_id=organization_id,
                file_path=file_path,
                ref=ref
            )
            runtime = util_models.RuntimeOptions()
            headers = {}

            response = self.client.get_file_blobs_with_options(repository_id, request, headers, runtime)

            if response.body and response.body.result:
                # 获取文件内容，通常在content字段中
                if hasattr(response.body.result, 'content'):
                    return response.body.result.content
                elif hasattr(response.body.result, 'blob_content'):
                    return response.body.result.blob_content

            return None

        except Exception as e:
            print(f"获取文件内容时发生错误 ({file_path}): {e}")
            return None


class URLParser:
    """URL解析器"""
    
    @staticmethod
    def parse_codeup_url(url: str) -> Optional[Dict[str, str]]:
        """
        解析CodeUp URL
        例如: https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs
        """
        pattern = r'https://codeup\.aliyun\.com/([^/]+)/([^/]+)/([^/]+)/change/(\d+)'
        match = re.match(pattern, url)
        
        if match:
            organization_id = match.group(1)
            namespace = match.group(2)
            project_name = match.group(3)
            local_id = match.group(4)
            
            path_with_namespace = f"{organization_id}/{namespace}/{project_name}"
            
            return {
                'organization_id': organization_id,
                'path_with_namespace': path_with_namespace,
                'local_id': int(local_id)
            }
        
        return None


class AICodeReviewer:
    """AI代码审查器"""

    def __init__(self, model: Optional[str] = None):
        self.api_key = LLM_API_KEY
        self.base_url = LLM_BASE_URL
        self.model = model or LLM_MODEL
        self.system_instruction = load_system_instruction()
    
    def review_diff(self, diff_content: str, file_path: str, full_file_content: Optional[str] = None) -> str:
        """对diff内容进行AI审查，可选择性地包含完整文件内容"""
        if len(diff_content.strip()) <= 10:
            return f"diff内容过少，跳过审查: {file_path}"

        # 构建用户消息内容
        print(f"\n\n开始AI评审文件:{file_path}\n{diff_content}\n")
        user_content = f"以下是文件 {file_path} 的diff内容，请进行代码审查：\n\n{diff_content}"

        # 如果有完整文件内容，添加到消息中
        if full_file_content:
            user_content += f"\n\n=== 完整文件内容（用于更好理解上下文） ===\n{full_file_content}"

        url = f"{self.base_url}/chat/completions"
        payload = {
            "model": self.model,
            "max_tokens": 16384,
            "top_p": 1,
            "top_k": 40,
            "presence_penalty": 0,
            "frequency_penalty": 0,
            "temperature": 0.9,
            "stream": True,
            "messages": [
                {
                    "role": "system",
                    "content": self.system_instruction,
                },
                {
                    "role": "user",
                    "content": user_content,
                },
            ],
        }
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }
        
        try:
            response = requests.post(url, headers=headers, json=payload, stream=True)
            response.raise_for_status()
            
            full_content = []
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith('data: '):
                        json_str = decoded_line[len('data: '):]
                        if json_str.strip() == '[DONE]':
                            break
                        try:
                            chunk = json.loads(json_str)
                            if chunk.get("choices"):
                                delta = chunk["choices"][0].get("delta", {})
                                content_piece = delta.get("content")
                                if content_piece:
                                    print(content_piece, end='', flush=True)
                                    full_content.append(content_piece)
                        except json.JSONDecodeError:
                            continue
            
            return "".join(full_content) if full_content else "AI审查返回空内容"
            
        except Exception as e:
            return f"AI审查失败: {str(e)}"
        finally:
            if 'response' in locals():
                response.close()


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="合并请求代码审查工具")
    parser.add_argument(
        "--model",
        type=str,
        help=f"指定AI模型 (默认: {LLM_MODEL})"
    )
    parser.add_argument(
        "--url",
        type=str,
        help="CodeUp合并请求URL"
    )
    return parser.parse_args()


def main():
    """主函数"""
    print("=== 合并请求代码审查工具 ===")

    # 解析命令行参数
    args = parse_arguments()

    # 获取URL - 优先使用命令行参数，否则交互式输入
    if args.url:
        url = args.url.strip()
        print(f"使用命令行提供的URL: {url}")
    else:
        url = input("请输入CodeUp合并请求URL: ").strip()
    
    if not url:
        print("URL不能为空")
        return
    
    # 解析URL
    parser = URLParser()
    url_info = parser.parse_codeup_url(url)
    
    if not url_info:
        print("无法解析URL，请确保URL格式正确")
        print("示例: https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs")
        return
    
    print(f"解析结果:")
    print(f"  组织ID: {url_info['organization_id']}")
    print(f"  项目路径: {url_info['path_with_namespace']}")
    print(f"  合并请求ID: {url_info['local_id']}")
    
    # 初始化DevOps客户端
    devops_client = DevOpsClient()
    
    # 1. 获取repository ID
    print("\n1. 查找repository...")
    repo_id = devops_client.find_repository_by_path(
        url_info['organization_id'], 
        url_info['path_with_namespace']
    )
    
    if not repo_id:
        print("无法找到对应的repository")
        return
    
    print(f"找到repository ID: {repo_id}")
    
    # 2. 获取合并请求的commit IDs
    print("\n2. 获取合并请求的commits...")
    commits_info = devops_client.get_merge_request_commits(
        url_info['organization_id'],
        repo_id,
        url_info['local_id']
    )

    merge_source = commits_info.get('merge_source')
    merge_target = commits_info.get('merge_target')

    if not merge_source or not merge_target:
        print("未找到MERGE_SOURCE或MERGE_TARGET commit")
        print(f"MERGE_SOURCE: {merge_source}")
        print(f"MERGE_TARGET: {merge_target}")
        return

    print(f"找到commits:")
    print(f"  MERGE_SOURCE (新代码): {merge_source}")
    print(f"  MERGE_TARGET (旧代码): {merge_target}")

    # 3. 获取diff并进行AI审查
    print("\n3. 开始代码审查...")

    # 将MERGE_TARGET作为from_commit，MERGE_SOURCE作为to_commit
    # 这样diff显示的是从旧代码到新代码的变化
    from_commit = merge_target  # 旧代码
    to_commit = merge_source    # 新代码

    print(f"比较范围: {from_commit} (旧) -> {to_commit} (新)")

    compare_result = devops_client.get_compare_detail(
        repo_id,
        url_info['organization_id'],
        from_commit,
        to_commit
    )

    if compare_result and compare_result.get('diffs'):
        # 创建AI审查器，传递模型参数
        ai_reviewer = AICodeReviewer(model=args.model)

        # 显示使用的模型信息
        model_info = args.model if args.model else LLM_MODEL
        print(f"使用AI模型: {model_info}")

        overall_markdown = f"# 代码审查报告\n\n**合并请求**: {url}\n**使用模型**: {model_info}\n\n"

        for diff in compare_result['diffs']:
            if hasattr(diff, 'new_path') and hasattr(diff, 'diff'):
                file_path = diff.new_path
                diff_content = diff.diff

                print(f"\n审查文件: {file_path}")
                overall_markdown += f"## 文件: {file_path}\n\n"

                # 获取目标分支（旧代码）的完整文件内容
                print(f"  获取文件完整内容...")
                full_file_content = devops_client.get_file_content(
                    repo_id,
                    url_info['organization_id'],
                    file_path,
                    from_commit  # 使用旧代码的commit作为参考
                )

                if full_file_content:
                    print(f"  已获取完整文件内容 ({len(full_file_content)} 字符)")
                else:
                    print(f"  未能获取完整文件内容，仅使用diff进行审查")

                review_result = ai_reviewer.review_diff(diff_content, file_path, full_file_content)
                overall_markdown += f"{review_result}\n\n"

        # 保存审查结果 - 使用项目路径和合并请求ID拼接文件名
        # 将路径中的/替换为_，避免文件路径问题
        safe_path = url_info['path_with_namespace'].replace('/', '_')
        output_file = f"mr_code_review_{safe_path}_{url_info['local_id']}.md"
        with open(output_file, "w", encoding="utf-8") as f:
            f.write(overall_markdown)

        print(f"\n审查完成！{url}\n结果已保存到: {output_file}")
    else:
        print("未获取到diff信息")


if __name__ == "__main__":
    main()
